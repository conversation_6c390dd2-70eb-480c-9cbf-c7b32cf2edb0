import request from "@/utils/request";

//查询任务性质列表
export function flightPurposeList(query) {
  return request({
    url: "/system/flightPurpose/list",
    method: "get",
    params: query,
  });
}

//新增任务性质
export function addFlightPurpose(data) {
  return request({
    url: "/system/flightPurpose/add",
    method: "post",
    data: data,
  });
}

//删除任务性质
export function deleteFlightPurpose(ids) {
  return request({
    url: `/system/flightPurpose/delete/${ids}`,
    method: "post",
  });
}

//查询任务性质详情
export function getFlightPurposeDetail(id) {
  return request({
    url: `/system/flightPurpose/getOne/${id}`,
    method: "get",
  });
}

//查询所有任务性质
export function queryAllFlightPurposes() {
  return request({
    url: "/system/flightPurpose/queryAll",
    method: "get",
  });
}

//修改任务性质
export function updateFlightPurpose(data) {
  return request({
    url: "/system/flightPurpose/update",
    method: "post",
    data: data,
  });
}
