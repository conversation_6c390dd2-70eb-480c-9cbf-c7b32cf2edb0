import request from "@/utils/request";

//查询机场信息列表
export function airportList(query) {
  return request({
    url: "/system/airport/list",
    method: "get",
    params: query,
  });
}

//新增机场
export function addAirport(data) {
  return request({
    url: "/system/airport/add",
    method: "post",
    data: data,
  });
}

//删除机场信息
export function deleteAirport(ids) {
  return request({
    url: `/system/airport/delete/${ids}`,
    method: "post",
  });
}

//查询机场详情
export function getAirportDetail(id) {
  return request({
    url: `/system/airport/getOne/${id}`,
    method: "post",
  });
}

//查询所有机场信息
export function queryAllAirports() {
  return request({
    url: "/system/airport/queryAll",
    method: "get",
  });
}

//修改机场信息
export function updateAirport(data) {
  return request({
    url: "/system/airport/update",
    method: "post",
    data: data,
  });
}
