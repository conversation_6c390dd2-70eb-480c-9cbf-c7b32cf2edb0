<template>
  <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <el-row :gutter="24">
        <el-col :span="8" v-show="!isBatch">
          <el-form-item label="任务日期" prop="flightDate" style="height: 36px">
            <el-date-picker
              clearable
              v-model="form.flightDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择任务日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-show="isBatch">
          <el-form-item label="批量起始" prop="flightDateBatchStart">
            <el-date-picker
              clearable
              v-model="form.flightDateBatchStart"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="设置开始任务日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-show="isBatch">
          <el-form-item label="批量结束" prop="flightDateBatchEnd">
            <el-date-picker
              clearable
              v-model="form.flightDateBatchEnd"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="设置结束任务日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="机型" prop="act">
            <el-select
              v-model="form.aircraftStyle"
              placeholder="请选择机型"
              disabled
            >
              <el-option
                v-for="item in aircraftList"
                :key="item.aircraftId"
                :label="item.aircraftStyle"
                :value="item.aircraftStyle"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="机号" prop="aircraftId">
            <el-select
              v-model="form.aircraftId"
              placeholder="请选择机号"
              @change="changeTaiNO"
            >
              <el-option
                v-for="item in aircraftList"
                :key="item.aircraftTailNo"
                :label="item.aircraftTailNo"
                :value="item.aircraftId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="计划起飞时间" prop="planDepartTime">
            <el-time-picker
              clearable
              placeholder="选择时间"
              v-model="form.planDepartTime"
              value-format="HH:mm"
            ></el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预计起飞时间" prop="planArriveTime">
            <el-time-picker
              clearable
              placeholder="选择时间"
              v-model="form.planArriveTime"
              value-format="HH:mm"
            ></el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务性质" prop="flightPurpose">
            <el-select
              v-model="form.flightPurpose"
              placeholder="请选择任务性质"
              filterable
            >
              <el-option
                v-for="item in flightPurposeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-show="type === 2">
        <el-col :span="8">
          <el-form-item label="值班经理" prop="dutyManagerId">
            <el-select
              v-model="form.dutyManagerId"
              placeholder="请选择值班经理"
              filterable
            >
              <el-option
                v-for="item in selectData['dutyManager']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="营业时间" prop="businessHours">
            <el-input
              v-model="form.businessHours"
              placeholder="请输入营业时间"
              clearable
            >
            </el-input> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="起飞点" prop="departCity">
            <el-select
              v-model="form.departCity"
              placeholder="请选择起飞点"
              filterable
            >
              <el-option
                v-for="item in airport"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="type === 1">
          <el-form-item label="降落点" prop="arriveCity">
            <el-select
              v-model="form.arriveCity"
              placeholder="请选择降落点"
              filterable
            >
              <el-option
                v-for="item in airport"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="type === 2">
          <el-form-item label="航线-空域" prop="route">
            <el-input
              v-model="form.route"
              placeholder="请输入航线-空域"
              clearable
            >
            </el-input> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="运行标准" prop="dperationStandard">
            <el-select
              v-model="form.dperationStandard"
              placeholder="请选择运行标准"
              filterable
            >
              <el-option
                v-for="item in dperationStandardList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="机长" prop="pilotType">
            <el-col :span="12" style="padding: 0">
              <el-select
                filterable
                v-model="form.pilotType"
                placeholder="请选择机长"
              >
                <el-option :value="1" label="机长"></el-option>
                <el-option :value="2" label="实习机长"></el-option>
                <el-option :value="3" label="教员"></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" style="padding: 0">
              <el-form-item prop="captainUserId" style="margin: 0">
                <el-select
                  style="width: 100%"
                  filterable
                  v-model="form.captainUserId"
                  placeholder="请选择"
                  collapse-tags
                  multiple
                >
                  <el-option
                    v-for="item in selectData.pilot"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="副驾驶" prop="captainUserId">
            <el-col :span="12" style="padding: 0">
              <el-select
                filterable
                collapse-tags
                v-model="form.copilotType"
                placeholder="请选择副驾驶"
              >
                <el-option :value="1" label="副驾驶"></el-option>
                <el-option :value="2" label="学员"></el-option>
                <el-option :value="3" label="同乘"></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" style="padding: 0">
              <el-form-item prop="copilotUserId" style="margin: 0">
                <el-select
                  style="width: 100%"
                  filterable
                  v-model="form.copilotUserId"
                  placeholder="请选择"
                  collapse-tags
                  multiple
                >
                  <el-option
                    v-for="item in selectData.pilot"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="放行员" prop="maintenanceId">
            <el-select
              v-model="form.maintenanceId"
              placeholder="请选择放行员"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['maintenance']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8" v-show="type === 1">
          <el-form-item label="机械师" prop="mechanicMasterId">
            <el-select
              v-model="form.mechanicMasterId"
              placeholder="请选择机械师"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['maintenance']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="机械员" prop="mechanicId">
            <el-select
              v-model="form.mechanicId"
              placeholder="请选择机械员"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['maintenance']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="保障员" prop="safetyOfficerId">
            <el-select
              v-model="form.safetyOfficerId"
              placeholder="请选择保障员"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['maintenance']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24" v-show="type === 2">
        <el-col :span="8">
          <el-form-item label="售票员" prop="conductorId">
            <el-select
              v-model="form.conductorId"
              placeholder="请选择售票员"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['conductor']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="安检员" prop="inspectorId">
            <el-select
              v-model="form.inspectorId"
              placeholder="请选择安检员"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['inspector']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="现场组织" prop="organizationId">
            <el-select
              v-model="form.organizationId"
              placeholder="请选择现场组织"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['organization']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="责任运控" prop="operationControlId">
            <el-select
              v-model="form.operationControlId"
              placeholder="请选择责任运控"
              filterable
              collapse-tags
              multiple
            >
              <el-option
                v-for="item in selectData['operationControl']"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注"
            /> </el-form-item
        ></el-col>
      </el-row>
      <!--      <el-row :gutter="24">-->
      <!--        <el-col :span="8"></el-col>-->
      <!--        <el-col :span="8"></el-col>-->
      <!--        <el-col :span="8"></el-col>-->
      <!--      </el-row>-->
      <!--      <el-row :gutter="24">-->
      <!--        <el-col :span="8"></el-col>-->
      <!--        <el-col :span="8"></el-col>-->
      <!--        <el-col :span="8"></el-col>-->
      <!--      </el-row>-->
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  dperationStandard,
  listAircraft,
  queryAllAirport,
  queryAllFlightPurpose,
} from "@/api/system/aircraft";
import {
  addFlightplan,
  getFlightplan,
  updateFlightplan,
} from "@/api/system/flightplan";
import { listUserByRole } from "@/api/system/wxuser";

export default {
  name: "AddFlightPlan",
  props: {
    title: String,
    open: Boolean,
    isBatch: Boolean,
    type: Number, //1A->B 2空中游览
    cancel: Function,
    flightplanId: Number | String,
  },
  data() {
    return {
      form: {},
      rules: {},
      aircraftList: [], //机型
      airport: [], //起飞点 降落点
      dperationStandardList: [], //运行标准
      selectData: {}, //人员数据
      flightPurposeList: [], //任务类型
    };
  },
  created() {
    this.getData();
  },
  watch: {
    open(newVal, oldVal) {
      this.form = {};
    },
    flightplanId() {
      this.flightplanId && this.getDetail(this.flightplanId);
    },
  },
  methods: {
    //获取修改的数据
    getDetail(flightplanId) {
      const that = this;
      getFlightplan(flightplanId).then((response) => {
        that.form = response.data;
        that.form.captainUserId = that.form.captainUserId
          ? that.form.captainUserId.split(",").map(Number)
          : [];
        that.form.captainUserId = that.form.captainUserId
          ? that.form.captainUserId.split(",").map(Number)
          : [];
        that.form.copilotUserId = that.form.copilotUserId
          ? that.form.copilotUserId.split(",").map(Number)
          : [];
        that.form.maintenanceId = that.form.maintenanceId
          ? that.form.maintenanceId.split(",").map(Number)
          : [];
        that.form.mechanicMasterId = that.form.mechanicMasterId
          ? that.form.mechanicMasterId.split(",").map(Number)
          : [];
        that.form.mechanicId = that.form.mechanicId
          ? that.form.mechanicId.split(",").map(Number)
          : [];
        that.form.safetyOfficerId = that.form.safetyOfficerId
          ? that.form.safetyOfficerId.split(",").map(Number)
          : [];
        that.form.conductorId = that.form.conductorId
          ? that.form.conductorId.split(",").map(Number)
          : [];
        that.form.inspectorId = that.form.inspectorId
          ? that.form.inspectorId.split(",").map(Number)
          : [];
        that.form.organizationId = that.form.organizationId
          ? that.form.organizationId.split(",").map(Number)
          : [];
        that.form.operationControlId = that.form.operationControlId
          ? that.form.operationControlId.split(",").map(Number)
          : [];
        that.form.flightPurpose = parseInt(that.form.flightPurpose);
      });
    },
    //获取下拉数据
    getData() {
      listUserByRole().then((response) => {
        this.selectData = response.data;
      });
      //起飞点降落点
      queryAllAirport().then((res) => {
        this.airport = res.data;
      });
      //任务性质
      queryAllFlightPurpose().then((response) => {
        this.flightPurposeList = response.data;
      });
      //运行标准
      dperationStandard({ pageNum: 1, pageSize: 20 }).then((response) => {
        this.dperationStandardList = response;
      });
      //获取机型
      listAircraft({ pageNum: 1, pageSize: 20 }).then((response) => {
        this.aircraftList = response.rows;
      });
    },
    // 改变机号获取机型机号
    changeTaiNO(type) {
      // console.log(type);
      var msg = this.aircraftList.filter((i) => {
        if (i.aircraftId == type) {
          return i;
        }
      });
      this.form.aircraftStyle = msg[0].aircraftStyle;
    },
    handleSubmit() {
      let that = this;
      console.log(that.flightplanId);
      that.$refs["form"].validate((valid) => {
        if (valid) {
          var params = that.form;
          params.captainUserId = params.captainUserId
            ? params.captainUserId.join(",")
            : "";
          params.captainUserId = params.captainUserId
            ? params.captainUserId.join(",")
            : " ";
          params.copilotUserId = params.copilotUserId
            ? params.copilotUserId.join(",")
            : " ";
          params.maintenanceId = params.maintenanceId
            ? params.maintenanceId.join(",")
            : "";
          params.mechanicMasterId = params.mechanicMasterId
            ? params.mechanicMasterId.join(",")
            : "";
          params.mechanicId = params.mechanicId
            ? params.mechanicId.join(",")
            : "";
          params.safetyOfficerId = params.safetyOfficerId
            ? params.safetyOfficerId.join(",")
            : "";
          params.conductorId = params.conductorId
            ? params.conductorId.join(",")
            : "";
          params.inspectorId = params.inspectorId
            ? params.inspectorId.join(",")
            : "";
          params.organizationId = params.organizationId
            ? params.organizationId.join(",")
            : "";
          params.operationControlId = params.operationControlId
            ? params.operationControlId.join(",")
            : "";

          // params = JSON.parse(JSON.stringify(that.form));

          // params.aircraftList = params.aircraftList;
          console.log(params);
          // return false
          if (String(that.flightplanId)) {
            updateFlightplan(params).then((response) => {
              that.msgSuccess("修改成功");
              that.cancel(true);
            });
          } else {
            addFlightplan(params).then((response) => {
              that.msgSuccess("新增成功");
              that.cancel(true);
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped></style>
