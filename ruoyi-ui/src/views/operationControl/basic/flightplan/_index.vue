<template>
  <div class="app-container">
    <!-- <Test :parentData = 'options'></Test> -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="任务类型" prop="flightPurpose">
        <el-select
          v-model="queryParams.flightPurpose"
          placeholder="请选择任务类型"
          filterable
        >
          <el-option
            v-for="item in flightPurposeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="航线类型" prop="flightPurpose">
        <el-select
          v-model="queryParams.routeType"
          placeholder="请选择航线类型"
          filterable
        >
          <el-option
            v-for="item in routeTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="起始城市" prop="departCity">
        <el-select
          v-model="queryParams.departCity"
          placeholder="请选择起始城市"
          filterable
        >
          <el-option
            v-for="item in airport"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="到达城市" prop="arriveCity">
        <el-select
          v-model="queryParams.arriveCity"
          placeholder="请选择到达城市"
          filterable
        >
          <el-option
            v-for="item in airport"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="航班号" prop="flightNo">
        <el-input
          v-model="queryParams.flightNo"
          placeholder="请输入航班号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="呼号" prop="callSign">
        <el-input
          v-model="queryParams.callSign"
          placeholder="请输入呼号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="机尾号" prop="aircraftTailNo">
        <el-input
          v-model="queryParams.aircraftTailNo"
          placeholder="请输入机尾号"
          clearable
          size="small"
        />
      </el-form-item>

      <el-form-item label="生效日期" prop="validDate">
        <el-date-picker
          v-model="queryParams.validDate"
          clearable
          size="small"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择生效日期"
        />
      </el-form-item>
      <el-form-item label="失效日期" prop="expireDate">
        <el-date-picker
          v-model="queryParams.expireDate"
          clearable
          size="small"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择失效日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:flightplan:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(false)"
        >
          新增飞行任务
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:flightplan:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd(true)"
        >
          批量新增飞行任务
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:flightplan:edit']"
        >修改
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:flightplan:remove']"
        >删除
        </el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:flightplan:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="flightplanList"
      style="width: 100%; margin-bottom: 10px"
      row-key="flightplanId"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="航班日期"
        align="center"
        prop="flightDate"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.flightDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="起始城市"
        width="130"
        align="center"
        prop="departCity"
      />

      <el-table-column
        label="到达城市"
        width="130"
        align="center"
        prop="arriveCity"
      />

      <el-table-column
        label="备降城市"
        width="130"
        align="center"
        prop="alternateCity"
      />
      <el-table-column label="任务类型" align="center" prop="flightPurpose">
        <template slot-scope="scope">
          <span>{{ scope.row.flightPurpose }}</span>
        </template>
      </el-table-column>
      <el-table-column label="航班号" align="center" prop="flightNo" />
      <el-table-column label="呼号" align="center" prop="callSign" />

      <el-table-column label="机尾号" align="center" prop="aircraftTailNo" />

      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:flightplan:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 1)"
          >
            查看/修改
          </el-button>
          <el-button
            v-hasPermi="['system:flightplan:edit']"
            size="mini"
            type="text"
            icon="el-icon-document-copy"
            @click="handleUpdate(scope.row, 2)"
          >
            复制
          </el-button>
          <el-button
            v-hasPermi="['system:sorties:add']"
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleCheckMsg(scope.row)"
          >
            机组信息
          </el-button>
          <el-button
            v-hasPermi="['system:flightplan:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改航班计划信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-col :span="24">
              <el-col v-show="!isBatch" :span="8">
                <el-form-item
                  label="航班日期"
                  prop="flightDate"
                  style="height: 36px"
                >
                  <el-date-picker
                    v-model="form.flightDate"
                    clearable
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择航班日期"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="isBatch" :span="8">
                <el-form-item label="批量起始" prop="flightDateBatchStart">
                  <el-date-picker
                    v-model="form.flightDateBatchStart"
                    clearable
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="设置开始航班日期"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="isBatch" :span="8">
                <el-form-item label="批量结束" prop="flightDateBatchEnd">
                  <el-date-picker
                    v-model="form.flightDateBatchEnd"
                    clearable
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="设置结束航班日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="航线类型" prop="routeType">
                  <el-select
                    v-model="form.routeType"
                    filterable
                    placeholder="请选择航线类型"
                  >
                    <el-option
                      v-for="item in routeTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24" v-show="form.routeType === '2'">
              <el-col :span="8">
                <el-form-item label="安检员">
                  <el-select
                    v-model="form.inspectorId"
                    size="small"
                    filterable
                    placeholder="请选择安检员"
                    multiple
                  >
                    <el-option
                      v-for="item in inspectorList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="售票员">
                  <el-select
                    v-model="form.conductorId"
                    size="small"
                    filterable
                    placeholder="请选择售票员"
                    multiple
                  >
                    <el-option
                      v-for="item in conductorList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="现场组织人员">
                  <el-select
                    v-model="form.organizationId"
                    size="small"
                    filterable
                    placeholder="请选择现场组织人员"
                    multiple
                  >
                    <el-option
                      v-for="item in organizationList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item
                  label="计划起飞时间"
                  prop="planDepartTime"
                  style="height: 36px"
                >
                  <el-time-picker
                    v-model="form.planDepartTime"
                    clearable
                    size="small"
                    placeholder="选择时间"
                    value-format="HH:mm"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="计划到达时间"
                  prop="planArriveTime"
                  style="height: 36px"
                >
                  <el-time-picker
                    v-model="form.planArriveTime"
                    clearable
                    size="small"
                    placeholder="选择时间"
                    value-format="HH:mm"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="任务类型" prop="flightPurpose">
                  <el-select
                    v-model="form.flightPurpose"
                    filterable
                    placeholder="请选择任务类型"
                  >
                    <el-option
                      v-for="item in flightPurposeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item label="呼号" prop="callSign">
                  <el-select
                    v-model="form.callSign"
                    placeholder="请选择飞行器呼号"
                  >
                    <el-option
                      v-for="item in aircraftList"
                      :key="item.aircraftTailNo"
                      :label="item.aircraftTailNo"
                      :value="item.aircraftTailNo"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="航班号" prop="flightNo">
                  <el-select
                    v-model="form.flightNo"
                    placeholder="请选择飞行器航班号"
                  >
                    <el-option
                      v-for="item in aircraftList"
                      :key="item.aircraftTailNo"
                      :label="item.aircraftTailNo"
                      :value="item.aircraftTailNo"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="起始城市" prop="departCity">
                  <!-- <el-input v-model="form.departCity" placeholder="请输入起始城市"/> -->
                  <el-select
                    v-model="form.departCity"
                    filterable
                    placeholder="请选择起始城市"
                    @change="cityChange($event, 1)"
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="起始三字码"
                  prop="departAirportCode"
                  :disabled="true"
                  style="display: none"
                >
                  <el-select
                    v-model="form.departAirportCode"
                    :disabled="true"
                    placeholder="请选择起始城市三字码"
                    filterable
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.threeAirportCode"
                      :label="item.threeAirportCode"
                      :value="item.threeAirportCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item label="到达城市" prop="arriveCity">
                  <el-select
                    v-model="form.arriveCity"
                    placeholder="请选择到达城市"
                    filterable
                    @change="cityChange($event, 2)"
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="到达三字码"
                  prop="arriveAirportCode"
                  :disabled="true"
                  style="display: none"
                >
                  <el-select
                    v-model="form.arriveAirportCode"
                    :disabled="true"
                    placeholder="请选择到达城市三字码"
                    filterable
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.threeAirportCode"
                      :label="item.threeAirportCode"
                      :value="item.threeAirportCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="备降城市" prop="alternateCity">
                  <el-select
                    v-model="form.alternateCity"
                    multiple
                    placeholder="请选择备降城市"
                    filterable
                    @change="cityChange($event, 3)"
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="备降三字码"
                  prop="alternateAirportCode"
                  style="display: none"
                >
                  <el-select
                    v-model="form.alternateAirportCode"
                    multiple
                    placeholder="请选择备降城市三字码"
                    filterable
                  >
                    <el-option
                      v-for="item in airport"
                      :key="item.threeAirportCode"
                      :label="item.threeAirportCode"
                      :value="item.threeAirportCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item label="运行标准" prop="dperationStandard">
                  <!-- <el-input v-model="form.dperationStandard" placeholder="请输入运行标准123预留待确认"/> -->
                  <el-select
                    v-model="form.dperationStandard"
                    placeholder="请选择运行标准"
                    filterable
                  >
                    <el-option
                      v-for="item in dperationStandardList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="责任机长" prop="captainUserId">
                  <el-select
                    v-model="form.captainUserId"
                    size="small"
                    filterable
                    multiple
                    placeholder="请选择责任机长"
                  >
                    <el-option
                      v-for="item in captainUserList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    >
                      <span style="float: left">{{ item.nickName }}</span>
                      <span
                        v-if="item.monthFlyTime > 95"
                        style="float: right; color: red; font-size: 13px"
                        >{{ "月飞行：" + item.monthFlyTime + "小时" }}</span
                      >
                      <span
                        v-if="item.weekFlyTime > 35"
                        style="
                          float: right;
                          color: red;
                          font-size: 13px;
                          margin-right: 8px;
                        "
                        >{{ "周飞行：" + item.weekFlyTime + "小时" }}</span
                      >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="副驾驶" prop="maintenanceId">
                  <el-select
                    v-model="form.copilotType"
                    style="width: 40%"
                    size="small"
                    filterable
                    placeholder="请选择"
                  >
                    <el-option value="0" label="副驾驶"> 副驾驶 </el-option>
                    <el-option value="1" label="副驾驶A"> 副驾驶A </el-option>
                  </el-select>
                  <el-select
                    v-model="form.copilotUserId"
                    style="width: 60%"
                    size="small"
                    filterable
                    placeholder="请选择"
                    collapse-tags
                    multiple
                  >
                    <el-option
                      v-for="item in copilotUserList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    >
                      <span style="float: left">{{ item.nickName }}</span>
                      <span
                        v-if="item.monthFlyTime > 95"
                        style="float: right; color: red; font-size: 13px"
                        >{{ "月飞行：" + item.monthFlyTime + "小时" }}</span
                      >
                      <span
                        v-if="item.weekFlyTime > 35"
                        style="
                          float: right;
                          color: red;
                          font-size: 13px;
                          margin-right: 8px;
                        "
                        >{{ "周飞行：" + item.weekFlyTime + "小时" }}</span
                      >
                    </el-option>
                  </el-select>
                  <!-- <el-select
                size="small"
                filterable
                v-model="form.copilotUserId"
                placeholder="请选择副驾驶"
                multiple
              >
                <el-option
                  v-for="item in copilotUserList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                >
                </el-option>
              </el-select> -->
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item label="放行机务" prop="maintenanceId">
                  <el-select
                    v-model="form.maintenanceId"
                    size="small"
                    filterable
                    placeholder="请选择放行机务"
                    multiple
                  >
                    <el-option
                      v-for="item in planMask"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="机械员" prop="maintenanceId">
                  <el-select
                    v-model="form.mechanicId"
                    size="small"
                    filterable
                    placeholder="请选择机械员"
                    multiple
                  >
                    <el-option
                      v-for="item in planMask"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="责任运控" prop="ocUserId">
                  <el-select
                    v-model="form.ocUserId"
                    filterable
                    placeholder="请选择责任运控"
                  >
                    <el-option
                      v-for="item in ocUserList"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
            <el-col :span="8">
              <el-form-item label="保障员" prop="maintenanceId">
                <el-select
                  v-model="form.safetyOfficerId"
                  size="small"
                  filterable
                  placeholder="请选择保障员"
                  multiple
                >
                  <el-option
                    v-for="item in planMask"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-col :span="8">
                <el-form-item label="机尾号" prop="aircraftId">
                  <el-select
                    v-model="form.aircraftTailNo"
                    placeholder="请选择飞行器机尾号"
                    @change="changeTaiNO"
                  >
                    <el-option
                      v-for="item in aircraftList"
                      :key="item.aircraftTailNo"
                      :label="item.aircraftTailNo"
                      :value="item.aircraftTailNo"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-col>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm"> 确 定 </el-button>
        <el-button @click="cancel"> 取 消 </el-button>
      </div>
    </el-dialog>
    <!-- 查看机组个人信息 -->
    <el-dialog
      title="查看机组信息"
      :visible.sync="Msgopen"
      width="70%"
      append-to-body
    >
      <el-row :gutter="10">
        <el-col
          v-for="(item, index) in dateList"
          :key="index"
          :span="24"
          style="border-bottom: 1px solid #efefef; margin-bottom: 10px"
        >
          <el-descriptions
            v-if="
              item.roleType == 1 ||
              item.roleType == 2 ||
              item.roleType == 3 ||
              item.roleType == 4
            "
            :title="'姓名:' + item.userName"
          >
            <!-- getStatus -->
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="执照有效期"
            >
              {{ item.validityOfLicense }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="体检合格证有效期"
            >
              {{ item.validityPeriodOfPhysicalExamination }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="熟练检查基准月"
            >
              {{ item.proficiencyCheckStandardMonth }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="熟练检查到期日"
            >
              {{ item.proficiencyCheckValidity }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="机长航线练检查到期日"
            >
              {{ item.validityPeriodOfRouteTrainingInspection }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="危险品训练有效期"
            >
              {{ item.dangerTrainingInspection }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 1 || item.roleType == 2"
              label="汉语言有效期"
            >
              {{ item.chineseLanguageValidity }}
            </el-descriptions-item>
            <el-descriptions-item label="空勤登记证有效期">
              {{ item.validityOfRegistrationCertificate }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 3 || item.roleType == 4"
              label="Y12放行授权有效期截止日期:"
            >
              {{ item.y12Inspection }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 3 || item.roleType == 4"
              label="C208放行授权有效期截止日期:"
            >
              {{ item.c208Inspection }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="item.roleType == 3 || item.roleType == 4"
              label="B300放行授权有效期截止日期:"
            >
              {{ item.b300Inspection }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="Msgopen = false"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFlightplan,
  getFlightplan,
  delFlightplan,
  addFlightplan,
  updateFlightplan,
  exportFlightplan,
  resetClick,
  sendJFMessage,
} from "@/api/system/flightplan";
// import Test from '@/components/Test'

import {
  listSorties,
  getSorties,
  delSorties,
  addSorties,
  updateSorties,
  exportSorties,
} from "@/api/system/sorties";
import {
  listAircraft,
  airport,
  dperationType,
  dperationStandard,
  flightPurpose,
} from "@/api/system/aircraft";
import { listUserByRole, listUserArr } from "@/api/system/wxuser";
import { getToken } from "@/utils/auth";
import {
  queryAllAirport,
  queryAllFlightPurpose,
} from "../../../../api/system/aircraft";

export default {
  name: "Flightplan",
  // components:{
  //   Test
  // },
  data() {
    return {
      dateList: [],
      dperationTypeList: [],
      dperationStandardList: [],
      flightPurposeList: [],
      airport: [],
      details: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedAircraft: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 航班计划信息表格数据
      flightplanList: [],
      // 飞行器信息表格数据
      aircraftList: [],
      captainUserList: [],
      copilotUserList: [],
      planMask: [],
      ocUserList: [],
      leaderUserList: [],
      generalleaderUserList: [],
      // 弹出层标题
      title: "",
      titleFlightSorties: "", // 班次的
      // 是否显示弹出层
      open: false,
      openFlightSorties: false, // 班次的
      // 是否显示批量
      isBatch: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        flightPurpose: null,
        routeType: null,
        flightNo: null,
        callSign: null,
        departAirportCode: null,
        departCity: null,
        arriveAirportCode: null,
        arriveCity: null,
        aircraftTailNo: null,
        validDate: null,
        expireDate: null,
      },
      preApprovalStatusList: [
        {
          value: 0,
          label: "未审批",
        },
        {
          value: 1,
          label: "同意",
        },
        {
          value: 2,
          label: "拒绝",
        },
      ],
      routeTypeList: [
        {
          value: "0",
          label: "单程",
        },
        {
          value: "1",
          label: "往返",
        },
        {
          value: "2",
          label: "空域",
        },
      ],
      flightPurposeList: [
        // {
        //   id: 1,
        //   value: "航空护林",
        //   label: "航空护林",
        // },
        // {
        //   id: 2,
        //   value: "短途运输",
        //   label: "短途运输",
        // },
        // {
        //   id: 3,
        //   value: "调机飞行",
        //   label: "调机飞行",
        // },
      ],
      // 资料导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        type: 1,
        flightsortiesId: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/file/importPdf",
      },
      formMsg: {},
      Msgopen: false,
      options: [],
      // 表单参数
      form: { routeType: "1" },
      formFlightSorties: {},
      formJFMessage: { type: 1, flightRange: 0 },
      // 表单校验
      rules: {},
      rulesFlightSorties: {},
      rulesJFMessage: {},
      captainUserId: ["cc"],
      copilotUserId: [],
      maintenanceId: [],
      organizationList: [],
      inspectorList: [],
      conductorList: [],
    };
  },
  computed: {},
  created() {
    this.listAircraft();
    queryAllAirport().then((res) => {
      this.airport = res.data;
      // this.aircraftList = response.rows;
    });
    queryAllFlightPurpose().then((response) => {
      this.flightPurposeList = response.data;
      this.getList();
    });
  },
  methods: {
    // 查看机组个人信息
    handleCheckMsg(row) {
      const that = this;
      this.Msgopen = true;
      console.log(row);
      var str =
        row.captainUserId +
        "," +
        row.copilotUserId +
        "," +
        row.maintenanceId +
        "," +
        row.safetyOfficerId +
        "/" +
        row.flightplanId;
      listUserArr(str).then((response) => {
        that.dateList = response.data;
      });
    },
    cityChange(item, type) {
      const that = this;
      if (type == 1) {
        var arr = [];
        arr = that.airport.filter((i) => {
          if (i.name == item) {
            return i;
          }
        });
        that.form.departAirportCode = arr[0].threeAirportCode;
      } else if (type == 2) {
        var arr = [];
        arr = that.airport.filter((i) => {
          if (i.name == item) {
            return i;
          }
        });
        that.form.arriveAirportCode = arr[0].threeAirportCode;
      } else if (type == 3) {
        var arr = [];
        that.airport.filter((i) => {
          item.map((o) => {
            if (i.name == o) {
              arr.push({
                threeAirportCode: i.threeAirportCode,
                alternateCity: i.name,
              });
            }
          });
        });
        const alternateAirportCode = [];
        const alternateCity = [];
        console.log(arr);
        // return false
        arr.map((m) => {
          console.log("备降" + m.threeAirportCode);
          alternateAirportCode.push(m.threeAirportCode);
          alternateCity.push(m.alternateCity);
        });
        that.form.alternateCity = alternateCity;
        that.form.alternateAirportCode = alternateAirportCode;
      }
      console.log(that.form.alternateCity, that.form.alternateAirportCode);
    },
    // 改变机尾号获取机型机号
    changeTaiNO(info) {
      const that = this;
      that.aircraftList.map((i) => {
        if (info == i.aircraftTailNo) {
          that.form.aircraftId = i.aircraftId;
        }
      });
    },

    resetClick(num, flightsortiesId) {
      const strMap = {
        1: "气象资料",
        2: "电子舱单",
        3: "放行单",
      };
      this.$confirm("是否重置" + strMap[num] + "为无数据状态?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          resetClick(num, flightsortiesId).then((response) => {});
        })
        .then(() => {
          this.msgSuccess("重置成功");
          this.getList();
        })
        .catch(() => {});
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (!row.flightSorties) {
        if (columnIndex === 0) {
          return [0, 0];
        } else if (columnIndex === 1) {
          return [1, 2];
        }
      }
    },
    /** 查询航班计划信息列表 */
    getList() {
      const that = this;
      that.loading = true;
      listFlightplan(this.queryParams).then((response) => {
        that.flightplanList = response.rows;
        // that.flightplanList.map((i, o) => {
        //   const num = parseInt(i.flightPurpose) - 1;
        //   i["purposeNames"] = that.flightPurposeList[num].name;
        // });
        that.total = response.total;
        that.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = { routeType: "1" };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // console.log(this.$children[0])
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.routeType = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.flightplanId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    listAircraft() {
      listAircraft({ pageNum: 1, pageSize: 120 }).then((response) => {
        this.aircraftList = response.rows;
      });
    },

    listUserByRole() {
      listUserByRole().then((response) => {
        this.captainUserList = response.data.pilot;
        this.copilotUserList = response.data.pilot;
        this.planMask = response.data.maintenance;
        this.ocUserList = response.data.operationControl;
        this.organizationList = response.data.organization;
        this.inspectorList = response.data.inspector;
        this.conductorList = response.data.conductor;
        // this.leaderUserList = response.data.值班领导;
        // this.generalleaderUserList = response.data.总值班领导;
      });
      dperationType({ pageNum: 1, pageSize: 120 }).then((response) => {
        this.dperationTypeList = response;
      });
      dperationStandard({ pageNum: 1, pageSize: 120 }).then((response) => {
        this.dperationStandardList = response;
      });
      queryAllFlightPurpose().then((response) => {
        this.flightPurposeList = response.data;
      });
    },

    /** 新增按钮操作 */
    handleAdd(isBatch) {
      this.reset();
      this.listUserByRole();

      // 查询出飞行器所以选择
      this.listAircraft();
      this.isBatch = isBatch;
      this.open = true;
      this.title = "添加飞行任务信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      const that = this;
      that.reset();
      that.isBatch = false;
      that.listUserByRole();

      that.listAircraft();
      const flightplanId = row.flightplanId || that.ids;

      getFlightplan(flightplanId).then((response) => {
        that.form = response.data;
        that.form.captainUserId = that.form.captainUserId
          ? that.form.captainUserId.split(",").map(Number)
          : [];
        that.form.copilotType = "" + that.form.copilotType;
        that.form.copilotUserId = that.form.copilotUserId
          ? that.form.copilotUserId.split(",").map(Number)
          : [];
        that.form.maintenanceId = that.form.maintenanceId
          ? that.form.maintenanceId.split(",").map(Number)
          : [];
        that.form.mechanicId = that.form.mechanicId
          ? that.form.mechanicId?.split(",").map(Number)
          : [];
        that.form.inspectorId = that.form.inspectorId
          ? that.form.inspectorId.split(",").map(Number)
          : [];
        that.form.conductorId = that.form.conductorId
          ? that.form.conductorId.split(",").map(Number)
          : [];
        that.form.organizationId = that.form.organizationId
          ? that.form.organizationId.split(",").map(Number)
          : [];
        that.form.safetyOfficerId = that.form.safetyOfficerId
          ? that.form.safetyOfficerId.split(",").map(Number)
          : [];
        that.form.alternateCity = [that.form.alternateCity];
        that.form.ocUserId = Number(that.form.ocUserId);
        that.form.alternateAirportCode =
          that.form.alternateAirportCode.split(",");
        that.form.routeType = that.form.routeType + "";
        that.form.flightPurpose = parseInt(that.form.flightPurpose);
        that.open = true;
        that.title = "修改飞行任务信息";
        that.form.flightplanId =
          Number(type) === 1 ? that.form.flightplanId : null;
      });
    },
    /** 提交按钮 */
    submitForm() {
      const that = this;
      const params = JSON.parse(JSON.stringify(that.form));
      params.captainUserId = params.captainUserId.join(",");
      params.copilotUserId = params.copilotUserId.join(",");
      params.maintenanceId = params.maintenanceId.join(",");
      params.safetyOfficerId = params.safetyOfficerId.join(",");
      params.alternateCity = params.alternateCity.join(",");
      params.alternateAirportCode = params.alternateAirportCode.join(",");
      params.mechanicId = params.mechanicId.join(",");
      params.inspectorId = params.inspectorId.join(",");
      params.conductorId = params.conductorId.join(",");
      params.organizationId = params.organizationId.join(",");

      console.log(params);
      if (params.flightplanId != null) {
        updateFlightplan(params).then((response) => {
          that.msgSuccess("修改成功");
          that.open = false;
          that.getList();
        });
      } else {
        addFlightplan(params).then((response) => {
          that.msgSuccess("新增成功");
          that.open = false;
          that.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const flightplanIds = row.flightplanId || this.ids;
      this.$confirm(
        '是否确认删除飞行任务信息编号为"' + flightplanIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delFlightplan(flightplanIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 飞行器信息序号 */
    rowAircraftIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 飞行器信息添加按钮操作 */
    handleAddAircraft() {
      const obj = {};
      obj.aircraftStyle = "";
      obj.aircraftId = "";
      obj.aircraftInfo = "";
      obj.aircraftCompany = "";
      obj.aircraftSeat = "";
      obj.aircraftLength = "";
      obj.aircraftHeight = "";
      obj.aircraftWidth = "";
      obj.aircraftMfRange = "";
      this.aircraftList.push(obj);
    },
    /** 飞行器信息删除按钮操作 */
    handleDeleteAircraft() {
      if (this.checkedAircraft.length === 0) {
        this.msgError("请先选择要删除的飞行器信息数据");
      } else {
        const aircraftList = this.aircraftList;
        const checkedAircraft = this.checkedAircraft;
        this.aircraftList = aircraftList.filter(function (item) {
          return checkedAircraft.indexOf(item.index) === -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleAircraftSelectionChange(selection) {
      this.checkedAircraft = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有飞行任务信息数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.exportLoading = true;
          return exportFlightplan(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
.app-container {
  ::v-deep {
    .el-table th {
      background: #8fbc8f;
    }

    .el-table__expanded-cell {
      border-bottom: 0px;
      border-right: 0px;
      padding: 0px 0px 0px 47px;
    }
  }

  .table-in-table {
    border-top: 0px;
  }
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner,
.el-select {
  width: 100%;
}
</style>
