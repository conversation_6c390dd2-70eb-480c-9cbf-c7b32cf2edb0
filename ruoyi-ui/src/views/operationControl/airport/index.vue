<template>
  <div>
    <el-table
      v-loading="loading"
      :data="flightplanList"
      style="width: 100%; margin-bottom: 10px"
      row-key="flightplanId"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="起始城市"
        width="130"
        align="center"
        prop="departCity"
      />

      <el-table-column
        label="到达城市"
        width="130"
        align="center"
        prop="arriveCity"
      />

      <el-table-column
        label="备降城市"
        width="130"
        align="center"
        prop="alternateCity"
      />
      <el-table-column label="任务类型" align="center" prop="flightPurpose">
        <template slot-scope="scope">
          <span>{{ scope.row.flightPurpose }}</span>
        </template>
      </el-table-column>
      <el-table-column label="航班号" align="center" prop="flightNo" />
      <el-table-column label="呼号" align="center" prop="callSign" />

      <el-table-column label="机尾号" align="center" prop="aircraftTailNo" />

      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:flightplan:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row, 1)"
          >
            查看/修改
          </el-button>
          <el-button
            v-hasPermi="['system:flightplan:edit']"
            size="mini"
            type="text"
            icon="el-icon-document-copy"
            @click="handleUpdate(scope.row, 2)"
          >
            复制
          </el-button>
          <el-button
            v-hasPermi="['system:sorties:add']"
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="handleCheckMsg(scope.row)"
          >
            机组信息
          </el-button>
          <el-button
            v-hasPermi="['system:flightplan:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: "Area",
  data() {
    return {
      loading: true,
      dataList: [],
    };
  },
  created() {},
  methods: {},
};
</script>
<style scoped></style>
